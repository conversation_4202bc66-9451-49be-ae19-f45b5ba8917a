package com.hl.archive.strategy;

import com.alibaba.fastjson2.JSONObject;
import com.hl.archive.listener.strategy.TaskHandleStrategy;
import com.hl.archive.listener.strategy.TaskHandleStrategyFactory;
import com.hl.archive.listener.strategy.impl.BusinessTripStrategy;
import com.hl.archive.listener.strategy.impl.LeaveRequestStrategy;
import com.hl.archive.listener.strategy.impl.OverseasTravelStrategy;
import com.hl.archive.listener.strategy.impl.PoliceAbilityStrategy;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 任务处理策略测试类
 */
@ExtendWith(MockitoExtension.class)
class TaskHandleStrategyTest {

    @Mock
    private ApplicationEventPublisher eventPublisher;

    @InjectMocks
    private TaskHandleStrategyFactory strategyFactory;

    @Test
    void testStrategyFactory() {
        // 创建策略实例
        LeaveRequestStrategy leaveRequestStrategy = new LeaveRequestStrategy(eventPublisher, null);
        BusinessTripStrategy businessTripStrategy = new BusinessTripStrategy(eventPublisher, null);
        PoliceAbilityStrategy policeAbilityStrategy = new PoliceAbilityStrategy(eventPublisher);
        OverseasTravelStrategy overseasTravelStrategy = new OverseasTravelStrategy(eventPublisher);

        List<TaskHandleStrategy> strategies = Arrays.asList(
                leaveRequestStrategy,
                businessTripStrategy,
                policeAbilityStrategy,
                overseasTravelStrategy
        );

        // 手动设置策略列表
        TaskHandleStrategyFactory factory = new TaskHandleStrategyFactory(strategies);
        factory.init();

        // 测试策略获取
        assertNotNull(factory.getStrategy("CC42ITDEHRQ"));
        assertNotNull(factory.getStrategy("CG9Z9HJ3FJW"));
        assertNotNull(factory.getStrategy("CNE2TZD2GTE"));
        assertNotNull(factory.getStrategy("CKSWWQQWW7H"));
        assertNull(factory.getStrategy("UNKNOWN"));

        // 测试策略支持检查
        assertTrue(factory.isSupported("CC42ITDEHRQ"));
        assertTrue(factory.isSupported("CG9Z9HJ3FJW"));
        assertTrue(factory.isSupported("CNE2TZD2GTE"));
        assertTrue(factory.isSupported("CKSWWQQWW7H"));
        assertFalse(factory.isSupported("UNKNOWN"));
    }

    @Test
    void testLeaveRequestStrategy() {
        LeaveRequestStrategy strategy = new LeaveRequestStrategy(eventPublisher, null);
        
        assertEquals("CC42ITDEHRQ", strategy.getSupportedConfigUuid());
        
        JSONObject contentData = new JSONObject();
        
        // 测试audit操作（由于LCBBService为null，会抛出异常，这里只测试事件发布）
        assertDoesNotThrow(() -> {
            try {
                strategy.handle(contentData, "audit");
            } catch (NullPointerException e) {
                // 预期的异常，因为LCBBService为null
            }
        });
        
        // 验证事件发布
        verify(eventPublisher, atLeastOnce()).publishEvent(any());
    }

    @Test
    void testBusinessTripStrategy() {
        BusinessTripStrategy strategy = new BusinessTripStrategy(eventPublisher, null);
        
        assertEquals("CG9Z9HJ3FJW", strategy.getSupportedConfigUuid());
        
        JSONObject contentData = new JSONObject();
        
        // 测试delete操作
        assertDoesNotThrow(() -> strategy.handle(contentData, "delete"));
        
        // 验证事件发布
        verify(eventPublisher, atLeastOnce()).publishEvent(any());
    }

    @Test
    void testPoliceAbilityStrategy() {
        PoliceAbilityStrategy strategy = new PoliceAbilityStrategy(eventPublisher);
        
        assertEquals("CNE2TZD2GTE", strategy.getSupportedConfigUuid());
        
        JSONObject contentData = new JSONObject();
        
        // 测试audit操作
        assertDoesNotThrow(() -> strategy.handle(contentData, "audit"));
        
        // 验证事件发布
        verify(eventPublisher, atLeastOnce()).publishEvent(any());
    }

    @Test
    void testOverseasTravelStrategy() {
        OverseasTravelStrategy strategy = new OverseasTravelStrategy(eventPublisher);
        
        assertEquals("CKSWWQQWW7H", strategy.getSupportedConfigUuid());
        
        JSONObject contentData = new JSONObject();
        
        // 测试audit操作
        assertDoesNotThrow(() -> strategy.handle(contentData, "audit"));
        
        // 验证事件发布
        verify(eventPublisher, atLeastOnce()).publishEvent(any());
    }
}
