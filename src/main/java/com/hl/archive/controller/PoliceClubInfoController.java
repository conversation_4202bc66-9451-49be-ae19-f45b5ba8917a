package com.hl.archive.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PoliceClubInfoQueryDTO;
import com.hl.archive.domain.entity.PoliceClubInfo;
import com.hl.archive.service.PoliceClubInfoService;
import com.hl.common.domain.R;
import com.hl.security.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/clubInfo")
@RequiredArgsConstructor
@Slf4j
@Api(tags = "社团管理")
public class PoliceClubInfoController {
    private final PoliceClubInfoService policeClubInfoService;


    @PostMapping("/page")
    @ApiOperation("列出社团信息")
    public R<List<PoliceClubInfo>> listClubInfo(@RequestBody PoliceClubInfoQueryDTO dto) {
        Page<PoliceClubInfo> page = policeClubInfoService.page(Page.of(dto.getPage(), dto.getLimit()), Wrappers.<PoliceClubInfo>lambdaQuery()
                .like(StrUtil.isNotBlank(dto.getName()), PoliceClubInfo::getName, dto.getName()));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/add")
    @ApiOperation("添加社团信息")
    public R<Boolean> addClubInfo(@RequestBody PoliceClubInfo request) {
        request.setCreatedBy(UserUtils.getUser().getIdCard());
        return R.ok(policeClubInfoService.save(request));
    }

    @PostMapping("/update")
    @ApiOperation("更新社团信息")
    public R<Boolean> updateClubInfo(@RequestBody PoliceClubInfo request) {
        request.setUpdatedBy(UserUtils.getUser().getIdCard());
        return R.ok(policeClubInfoService.updateById(request));
    }

    @PostMapping("/delete")
    @ApiOperation("删除社团信息")
    public R<Boolean> deleteClubInfo(@RequestBody PoliceClubInfo request) {
        request.setUpdatedBy(UserUtils.getUser().getIdCard());
        return R.ok(policeClubInfoService.removeById(request));
    }

}
