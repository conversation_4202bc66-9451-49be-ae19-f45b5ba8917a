package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 队伍风险隐患汇总
 */
@ApiModel(description="队伍风险隐患汇总")
@Data
@TableName(value = "police_archive.police_risk_summary")
public class PoliceRiskSummary {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 年度
     */
    @TableField(value = "annual")
    @ApiModelProperty(value="年度")
    private String annual;

    /**
     * 月份
     */
    @TableField(value = "`month`")
    @ApiModelProperty(value="月份")
    private String month;

    /**
     * 姓名
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value="姓名")
    private String name;

    /**
     * 单位名称
     */
    @TableField(value = "org_name")
    @ApiModelProperty(value="单位名称")
    private String orgName;

    /**
     * 身份证号码
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value="身份证号码")
    private String idCard;

    /**
     * 职务
     */
    @TableField(value = "`position`")
    @ApiModelProperty(value="职务")
    private String position;

    /**
     * 性别
     */
    @TableField(value = "gender")
    @ApiModelProperty(value="性别")
    private String gender;

    /**
     * 出生日期
     */
    @TableField(value = "birth_date")
    @ApiModelProperty(value="出生日期")
    private LocalDate birthDate;

    /**
     * 工作时间
     */
    @TableField(value = "work_date")
    @ApiModelProperty(value="工作时间")
    private LocalDate workDate;

    /**
     * 政治面貌
     */
    @TableField(value = "political_status")
    @ApiModelProperty(value="政治面貌")
    private String politicalStatus;

    /**
     * 入党团时间
     */
    @TableField(value = "join_party_date")
    @ApiModelProperty(value="入党团时间")
    private LocalDate joinPartyDate;

    /**
     * 职务层次(一)
     */
    @TableField(value = "duty_level1")
    @ApiModelProperty(value="职务层次(一)")
    private String dutyLevel1;

    /**
     * 职务层次(二)
     */
    @TableField(value = "duty_level2")
    @ApiModelProperty(value="职务层次(二)")
    private String dutyLevel2;

    /**
     * 职级
     */
    @TableField(value = "`rank`")
    @ApiModelProperty(value="职级")
    private String rank;

    /**
     * 警种岗位
     */
    @TableField(value = "police_post")
    @ApiModelProperty(value="警种岗位")
    private String policePost;

    /**
     * 风险状况
     */
    @TableField(value = "risk_status")
    @ApiModelProperty(value="风险状况")
    private String riskStatus;

    /**
     * 风险类型
     */
    @TableField(value = "risk_type")
    @ApiModelProperty(value="风险类型")
    private String riskType;

    /**
     * 风险等级
     */
    @TableField(value = "risk_level")
    @ApiModelProperty(value="风险等级")
    private String riskLevel;

    /**
     * 关爱举措
     */
    @TableField(value = "care_measures")
    @ApiModelProperty(value="关爱举措")
    private String careMeasures;

    /**
     * 关爱记录
     */
    @TableField(value = "care_record")
    @ApiModelProperty(value="关爱记录")
    private String careRecord;

    /**
     * 当月变化
     */
    @TableField(value = "monthly_change")
    @ApiModelProperty(value="当月变化")
    private String monthlyChange;

    /**
     * 处理处分时间
     */
    @TableField(value = "disposal_date")
    @ApiModelProperty(value="处理处分时间")
    private LocalDate disposalDate;

    /**
     * 处理处分分类
     */
    @TableField(value = "disposal_category")
    @ApiModelProperty(value="处理处分分类")
    private String disposalCategory;

    /**
     * 处理处分类别
     */
    @TableField(value = "disposal_type")
    @ApiModelProperty(value="处理处分类别")
    private String disposalType;

    /**
     * 责任领导
     */
    @TableField(value = "responsible_leader")
    @ApiModelProperty(value="责任领导")
    private String responsibleLeader;

    /**
     * 政工主管
     */
    @TableField(value = "political_commissar")
    @ApiModelProperty(value="政工主管")
    private String politicalCommissar;

    /**
     * 分管领导
     */
    @TableField(value = "charge_leader")
    @ApiModelProperty(value="分管领导")
    private String chargeLeader;

    /**
     * 家庭住址
     */
    @TableField(value = "home_address")
    @ApiModelProperty(value="家庭住址")
    private String homeAddress;

    /**
     * 联系电话
     */
    @TableField(value = "phone")
    @ApiModelProperty(value="联系电话")
    private String phone;

    /**
     * 家庭成员
     */
    @TableField(value = "family_members")
    @ApiModelProperty(value="家庭成员")
    private String familyMembers;

    /**
     * 是否删除
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value="是否删除")
    private Byte isDeleted;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value="创建人")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value="更新人")
    private String updatedBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value="更新时间")
    private LocalDateTime updatedAt;
}