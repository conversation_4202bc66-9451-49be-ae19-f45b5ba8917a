package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 违规违纪人员
 */
@ApiModel(description="违规违纪人员")
@Data
@TableName(value = "police_archive.police_violation_person")
public class PoliceViolationPerson {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 姓名
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value="姓名")
    private String name;

    /**
     * 性别
     */
    @TableField(value = "gender")
    @ApiModelProperty(value="性别")
    private String gender;

    /**
     * 出生日期
     */
    @TableField(value = "birth_date")
    @ApiModelProperty(value="出生日期")
    private LocalDate birthDate;

    /**
     * 立案时所在单位
     */
    @TableField(value = "case_org")
    @ApiModelProperty(value="立案时所在单位")
    private String caseOrg;

    /**
     * 警种部门
     */
    @TableField(value = "police_dept")
    @ApiModelProperty(value="警种部门")
    private String policeDept;

    /**
     * 职务
     */
    @TableField(value = "`position`")
    @ApiModelProperty(value="职务")
    private String position;

    /**
     * 职级
     */
    @TableField(value = "`rank`")
    @ApiModelProperty(value="职级")
    private String rank;

    /**
     * 政治面貌
     */
    @TableField(value = "political_status")
    @ApiModelProperty(value="政治面貌")
    private String politicalStatus;

    /**
     * 入党(团)时间
     */
    @TableField(value = "join_party_date")
    @ApiModelProperty(value="入党(团)时间")
    private LocalDate joinPartyDate;

    /**
     * 是否系被倒查问责
     */
    @TableField(value = "is_accountability")
    @ApiModelProperty(value="是否系被倒查问责")
    private String isAccountability;

    /**
     * 四种形态处理归类
     */
    @TableField(value = "disposition_category")
    @ApiModelProperty(value="四种形态处理归类")
    private String dispositionCategory;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @ApiModelProperty(value="备注")
    private String remark;

    /**
     * 是否删除
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value="是否删除")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value="创建人")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value="更新人")
    private String updatedBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value="更新时间")
    private LocalDateTime updatedAt;

    @TableField(value = "xxzjbh")
    private String xxzjbh;

    @TableField(value = "wt_xxzjbh")
    private String wtXxzjbh;
}