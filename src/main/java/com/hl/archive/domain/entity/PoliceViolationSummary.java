package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 违规违纪问题汇总
 */
@ApiModel(description="违规违纪问题汇总")
@Data
@TableName(value = "police_archive.police_violation_summary")
public class PoliceViolationSummary {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @TableField(value = "xxzjbh")
    private String xxzjbh;

    /**
     * 填报单位
     */
    @TableField(value = "report_org")
    @ApiModelProperty(value="填报单位")
    private String reportOrg;

    /**
     * 问题线索来源
     */
    @TableField(value = "clue_source")
    @ApiModelProperty(value="问题线索来源")
    private String clueSource;

    /**
     * 问题线索内容
     */
    @TableField(value = "clue_content")
    @ApiModelProperty(value="问题线索内容")
    private String clueContent;

    /**
     * 发现时间
     */
    @TableField(value = "found_date")
    @ApiModelProperty(value="发现时间")
    private LocalDate foundDate;

    /**
     * 发现单位
     */
    @TableField(value = "found_org")
    @ApiModelProperty(value="发现单位")
    private String foundOrg;

    /**
     * 受理时间
     */
    @TableField(value = "accept_date")
    @ApiModelProperty(value="受理时间")
    private LocalDate acceptDate;

    /**
     * 受理单位
     */
    @TableField(value = "accept_org")
    @ApiModelProperty(value="受理单位")
    private String acceptOrg;

    /**
     * 问题线索移交时间
     */
    @TableField(value = "transfer_date")
    @ApiModelProperty(value="问题线索移交时间")
    private LocalDate transferDate;

    /**
     * 移交接收单位
     */
    @TableField(value = "transfer_org")
    @ApiModelProperty(value="移交接收单位")
    private String transferOrg;

    /**
     * 启动调查时间
     */
    @TableField(value = "investigation_start")
    @ApiModelProperty(value="启动调查时间")
    private LocalDate investigationStart;

    /**
     * 调查单位
     */
    @TableField(value = "investigation_org")
    @ApiModelProperty(value="调查单位")
    private String investigationOrg;

    /**
     * 调查情况
     */
    @TableField(value = "investigation_result")
    @ApiModelProperty(value="调查情况")
    private String investigationResult;

    /**
     * 启动初核时间
     */
    @TableField(value = "preliminary_start")
    @ApiModelProperty(value="启动初核时间")
    private LocalDate preliminaryStart;

    /**
     * 初核单位
     */
    @TableField(value = "preliminary_org")
    @ApiModelProperty(value="初核单位")
    private String preliminaryOrg;

    /**
     * 初核情况
     */
    @TableField(value = "preliminary_result")
    @ApiModelProperty(value="初核情况")
    private String preliminaryResult;

    /**
     * 会商时间
     */
    @TableField(value = "meeting_date")
    @ApiModelProperty(value="会商时间")
    private LocalDate meetingDate;

    /**
     * 会商单位
     */
    @TableField(value = "meeting_org")
    @ApiModelProperty(value="会商单位")
    private String meetingOrg;

    /**
     * 会商结果
     */
    @TableField(value = "meeting_result")
    @ApiModelProperty(value="会商结果")
    private String meetingResult;

    /**
     * 立案时间
     */
    @TableField(value = "case_date")
    @ApiModelProperty(value="立案时间")
    private LocalDate caseDate;

    /**
     * 立案单位
     */
    @TableField(value = "case_org")
    @ApiModelProperty(value="立案单位")
    private String caseOrg;

    /**
     * 留置审查时间
     */
    @TableField(value = "detention_date")
    @ApiModelProperty(value="留置审查时间")
    private LocalDate detentionDate;

    /**
     * 留置审查单位
     */
    @TableField(value = "detention_org")
    @ApiModelProperty(value="留置审查单位")
    private String detentionOrg;

    /**
     * 主要违法违纪事实
     */
    @TableField(value = "violation_fact")
    @ApiModelProperty(value="主要违法违纪事实")
    private String violationFact;

    /**
     * 违规违纪类型
     */
    @TableField(value = "violation_type")
    @ApiModelProperty(value="违规违纪类型")
    private String violationType;

    /**
     * 案件编号
     */
    @TableField(value = "case_no")
    @ApiModelProperty(value="案件编号")
    private String caseNo;

    /**
     * 是否删除
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value="是否删除")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value="创建人")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value="更新人")
    private String updatedBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value="更新时间")
    private LocalDateTime updatedAt;
}