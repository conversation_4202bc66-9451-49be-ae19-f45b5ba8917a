package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 民警受伤信息申报
 */
@ApiModel(description="民警受伤信息申报")
@Data
@TableName(value = "police_archive.police_injury_declare")
public class PoliceInjuryDeclare {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 姓名
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value="姓名")
    private String name;

    /**
     * 警号
     */
    @TableField(value = "police_number")
    @ApiModelProperty(value="警号")
    private String policeNumber;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value="身份证号")
    private String idCard;

    /**
     * 工作单位
     */
    @TableField(value = "org_name")
    @ApiModelProperty(value="工作单位")
    private String orgName;

    /**
     * 职务
     */
    @TableField(value = "`position`")
    @ApiModelProperty(value="职务")
    private String position;

    /**
     * 申报类别
     */
    @TableField(value = "declare_type")
    @ApiModelProperty(value="申报类别")
    private String declareType;

    /**
     * 当前状态
     */
    @TableField(value = "current_status")
    @ApiModelProperty(value="当前状态")
    private String currentStatus;

    /**
     * 受伤事件
     */
    @TableField(value = "injury_event")
    @ApiModelProperty(value="受伤事件")
    private String injuryEvent;

    /**
     * 是否删除
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value="是否删除")
    private Byte isDeleted;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value="创建人")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value="更新人")
    private String updatedBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value="更新时间")
    private LocalDateTime updatedAt;
}