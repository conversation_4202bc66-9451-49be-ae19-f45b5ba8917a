package com.hl.archive.domain.entity;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 饮酒报备记录表
 */
@ApiModel(description="饮酒报备记录表")
@Data
@TableName(value = "police_drink_report")
public class PoliceDrinkReport {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value="身份证")
    private String idCard;

    /**
     * 饮酒时间
     */
    @TableField(value = "drink_time")
    @ApiModelProperty(value="饮酒时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime drinkTime;

    /**
     * 饮酒事由
     */
    @TableField(value = "reason")
    @ApiModelProperty(value="饮酒事由")
    private String reason;

    /**
     * 饮酒地点
     */
    @TableField(value = "`location`")
    @ApiModelProperty(value="饮酒地点")
    private String location;

    /**
     * 邀约人
     */
    @TableField(value = "inviter")
    @ApiModelProperty(value="邀约人")
    private String inviter;

    /**
     * 参加人数
     */
    @TableField(value = "participants")
    @ApiModelProperty(value="参加人数")
    private String participants;

    /**
     * 付款人
     */
    @TableField(value = "payer")
    @ApiModelProperty(value="付款人")
    private String payer;

    /**
     * 出行方式
     */
    @TableField(value = "travel_mode")
    @ApiModelProperty(value="出行方式")
    private String travelMode;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @ApiModelProperty(value="备注")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_at")
    @ApiModelProperty(value="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createAt;

    /**
     * 更新时间
     */
    @TableField(value = "update_at")
    @ApiModelProperty(value="更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value="创建人身份证号")
    private LocalDateTime createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value="更新人身份证号")
    private LocalDateTime updatedBy;

    /**
     * 是否删除
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value="是否删除")
    @TableLogic
    private Byte isDeleted;

    @TableField(value = "task_id")
    @ApiModelProperty(value="任务ID")
    private String taskId;


    @TableField(exist = false)
    @Translation(type = TransConstants.ID_CARD_TO_USER_OBJ, mapper = "idCard")
    private JSONObject personInfo;
}