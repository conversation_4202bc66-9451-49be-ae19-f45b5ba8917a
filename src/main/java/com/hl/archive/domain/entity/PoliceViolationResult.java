package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 违规违纪处罚结果
 */
@ApiModel(description="违规违纪处罚结果")
@Data
@TableName(value = "police_archive.police_violation_result")
public class PoliceViolationResult {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 信息主键编号
     */
    @TableField(value = "xxzjbh")
    @ApiModelProperty(value="信息主键编号")
    private String xxzjbh;

    /**
     * 问题信息主键编号
     */
    @TableField(value = "wt_xxzjbh")
    @ApiModelProperty(value="问题信息主键编号")
    private String wtXxzjbh;

    /**
     * 人员信息主键编号
     */
    @TableField(value = "ry_xxzjbh")
    @ApiModelProperty(value="人员信息主键编号")
    private String ryXxzjbh;

    /**
     * 类别名称
     */
    @TableField(value = "lbmc")
    @ApiModelProperty(value="类别名称")
    private String lbmc;

    /**
     * 处理时间
     */
    @TableField(value = "clsj")
    @ApiModelProperty(value="处理时间")
    private LocalDate clsj;

    /**
     * 处理单位
     */
    @TableField(value = "cldw")
    @ApiModelProperty(value="处理单位")
    private String cldw;

    /**
     * 处理结果
     */
    @TableField(value = "cljg")
    @ApiModelProperty(value="处理结果")
    private String cljg;

    /**
     * 是否删除
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value="是否删除")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value="创建人")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value="更新人")
    private String updatedBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value="更新时间")
    private LocalDateTime updatedAt;
}