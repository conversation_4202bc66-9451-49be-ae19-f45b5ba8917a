package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.archive.domain.entity.PoliceViolationPerson;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;

@Data
@TableName(value = "ZTJC.V_WJ_WGWJDJB_RYCL")
@AutoMapper(target = PoliceViolationPerson.class,uses = {ConversionUtils.class})
public class VWjWgwjdjbRycl {
    /**
     * 信息主键编号
     */
    @TableField(value = "XXZJBH")
    @AutoMapping(target = "xxzjbh")
    private String xxzjbh;

    /**
     * 问题主键编号
     */
    @TableField(value = "WT_XXZJBH")
    @AutoMapping(target = "wtXxzjbh")
    private String wtXxzjbh;

    /**
     * 姓名
     */
    @TableField(value = "XM")
    @AutoMapping(target = "name")
    private String xm;

    /**
     * 性别
     */
    @TableField(value = "XBMC")
    @AutoMapping(target = "gender")
    private String xbmc;

    /**
     * 出生日期
     */
    @TableField(value = "CSRQ")
    @AutoMapping(target = "birthDate",qualifiedByName = "strToLocalDate")
    private String csrq;

    /**
     * 立案时所在单位
     */
    @TableField(value = "DWMC")
    @AutoMapping(target = "caseOrg")
    private String dwmc;

    /**
     * 警种部门
     */
    @TableField(value = "JZBMMC")
    @AutoMapping(target = "policeDept")
    private String jzbmmc;

    /**
     * 职务
     */
    @TableField(value = "ZWMC")
    @AutoMapping(target = "position")
    private String zwmc;

    /**
     * 职级
     */
    @TableField(value = "ZJMC")
    @AutoMapping(target = "rank")
    private String zjmc;

    /**
     * 政治面貌
     */
    @TableField(value = "ZZMMMC")
    @AutoMapping(target = "politicalStatus")
    private String zzmmmc;

    /**
     * 入党(团)时间
     */
    @TableField(value = "RDRQ")
    @AutoMapping(target = "joinPartyDate",qualifiedByName = "strToLocalDate")
    private String rdrq;

    /**
     * 是否系被倒查问责
     */
    @TableField(value = "SFDCWZMC")
    @AutoMapping(target = "isAccountability")
    private String sfdcwzmc;

    /**
     * 四种形态处理归类
     */
    @TableField(value = "CLXTMC")
    @AutoMapping(target = "dispositionCategory")
    private String clxtmc;

    /**
     * 备注
     */
    @TableField(value = "BZ")
    @AutoMapping(target = "remark")
    private String bz;
}