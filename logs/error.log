2025-08-29 13:34:27.574 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:30.734 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:33.901 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:37.058 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:40.218 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:43.773 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:46.950 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:51.101 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:54.787 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:58.416 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:35:55.204 ERROR [RMI TCP Connection(14)-100.88.176.35] c.a.d.f.s.StatFilter - slow sql 1838 millis. SELECT 1[]
