2025-08-29 13:33:46.306 INFO  [main] c.a.n.c.e.SearchableProperties - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-29 13:33:46.363 INFO  [background-preinit] o.h.v.i.u.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-29 13:33:49.224 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-29 13:33:49.224 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-29 13:33:52.923 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
2025-08-29 13:33:52.932 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
2025-08-29 13:33:52.936 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - Ignore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
2025-08-29 13:33:52.938 INFO  [main] o.s.c.b.c.PropertySourceBootstrapConfiguration - Located property source: [BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive-druid.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive,default'}]
2025-08-29 13:33:52.985 INFO  [main] c.h.AppMain - The following 1 profile is active: "druid"
2025-08-29 13:33:56.278 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-29 13:33:56.291 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-29 13:33:56.405 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 80 ms. Found 0 Elasticsearch repository interfaces.
2025-08-29 13:33:56.415 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-29 13:33:56.418 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-29 13:33:56.463 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 43 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-29 13:33:56.492 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-29 13:33:56.498 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-29 13:33:56.550 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 35 ms. Found 0 Redis repository interfaces.
2025-08-29 13:33:57.292 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:57.293 WARN  [main] o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
2025-08-29 13:33:58.278 INFO  [main] o.s.c.c.s.GenericScope - BeanFactory id=345354b6-40df-36f1-977c-21a895912dba
2025-08-29 13:34:00.193 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-29 13:34:00.210 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-29 13:34:00.215 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$599/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-29 13:34:00.246 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-29 13:34:00.632 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dictProperties' of type [com.hl.dict.config.DictProperties$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-29 13:34:00.641 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicTableNameHandler' of type [com.hl.dict.config.DynamicTableNameHandler$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-29 13:34:02.073 INFO  [main] o.s.b.w.e.t.TomcatWebServer - Tomcat initialized with port(s): 28183 (http)
2025-08-29 13:34:02.126 INFO  [main] o.a.c.h.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-28183"]
2025-08-29 13:34:02.133 INFO  [main] o.a.c.c.StandardService - Starting service [Tomcat]
2025-08-29 13:34:02.133 INFO  [main] o.a.c.c.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-29 13:34:02.595 INFO  [main] o.a.c.c.C.[.[.[/] - Initializing Spring embedded WebApplicationContext
2025-08-29 13:34:02.595 INFO  [main] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 9579 ms
2025-08-29 13:34:03.168 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'sso-hl' URL not provided. Will try picking an instance via load-balancing.
2025-08-29 13:34:04.229 INFO  [main] c.h.d.c.MybatisPlusEnhancerConfiguration - ✅ Enhanced MybatisPlusInterceptor with DynamicTableNameInnerInterceptor.
2025-08-29 13:34:08.945 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
2025-08-29 13:34:08.945 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:08.964 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
2025-08-29 13:34:08.965 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:08.983 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
2025-08-29 13:34:08.983 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.002 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
2025-08-29 13:34:09.002 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.017 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
2025-08-29 13:34:09.018 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.036 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
2025-08-29 13:34:09.036 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.054 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
2025-08-29 13:34:09.054 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.071 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
2025-08-29 13:34:09.071 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.089 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
2025-08-29 13:34:09.089 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.106 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
2025-08-29 13:34:09.106 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.123 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
2025-08-29 13:34:09.124 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.143 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
2025-08-29 13:34:09.143 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.163 WARN  [main] c.b.m.c.m.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
2025-08-29 13:34:09.199 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
2025-08-29 13:34:09.199 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.224 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
2025-08-29 13:34:09.225 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.240 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
2025-08-29 13:34:09.240 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.257 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
2025-08-29 13:34:09.257 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.278 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
2025-08-29 13:34:09.278 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.297 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
2025-08-29 13:34:09.297 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.318 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
2025-08-29 13:34:09.319 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.336 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
2025-08-29 13:34:09.337 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.354 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
2025-08-29 13:34:09.354 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.369 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
2025-08-29 13:34:09.369 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.385 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
2025-08-29 13:34:09.385 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.437 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
2025-08-29 13:34:09.437 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.463 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
2025-08-29 13:34:09.463 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.478 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
2025-08-29 13:34:09.478 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.492 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
2025-08-29 13:34:09.493 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.510 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
2025-08-29 13:34:09.511 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.525 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
2025-08-29 13:34:09.526 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.541 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
2025-08-29 13:34:09.542 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.556 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
2025-08-29 13:34:09.557 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.571 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
2025-08-29 13:34:09.572 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.588 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
2025-08-29 13:34:09.588 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.603 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
2025-08-29 13:34:09.603 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.618 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
2025-08-29 13:34:09.619 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.634 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
2025-08-29 13:34:09.634 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.650 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
2025-08-29 13:34:09.650 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.680 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
2025-08-29 13:34:09.680 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.702 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
2025-08-29 13:34:09.702 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.720 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
2025-08-29 13:34:09.720 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.740 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
2025-08-29 13:34:09.740 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.759 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
2025-08-29 13:34:09.759 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.777 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
2025-08-29 13:34:09.777 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.795 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
2025-08-29 13:34:09.795 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.810 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
2025-08-29 13:34:09.811 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.827 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
2025-08-29 13:34:09.828 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.844 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
2025-08-29 13:34:09.844 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.873 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
2025-08-29 13:34:09.873 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.905 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
2025-08-29 13:34:09.905 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.935 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
2025-08-29 13:34:09.935 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.953 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
2025-08-29 13:34:09.954 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:09.971 WARN  [main] c.b.m.c.m.TableInfoHelper - Can not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
2025-08-29 13:34:09.971 WARN  [main] c.b.m.c.i.DefaultSqlInjector - class com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-29 13:34:10.558 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'hl-task' URL not provided. Will try picking an instance via load-balancing.
2025-08-29 13:34:24.039 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-29 13:34:27.574 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:27.575 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:27.696 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-29 13:34:30.734 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:30.735 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:30.861 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-29 13:34:33.901 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:33.901 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:34.025 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-29 13:34:37.058 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:37.058 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:37.192 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-29 13:34:40.218 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:40.218 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:40.739 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-29 13:34:43.773 ERROR [ForkJoinPool.commonPool-worker-2] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:43.774 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:43.906 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-29 13:34:46.950 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:46.950 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:47.071 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-29 13:34:51.101 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:51.101 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:51.550 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CG9Z9HJ3FJW -> BusinessTripStrategy
2025-08-29 13:34:51.551 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CC42ITDEHRQ -> LeaveRequestStrategy
2025-08-29 13:34:51.551 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CKSWWQQWW7H -> OverseasTravelStrategy
2025-08-29 13:34:51.551 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CNE2TZD2GTE -> PoliceAbilityStrategy
2025-08-29 13:34:51.551 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: CXQNJMIAR1C -> PoliceClubActivityStrategy
2025-08-29 13:34:51.551 INFO  [main] c.h.a.l.s.TaskHandleStrategyFactory - 注册任务处理策略: C30NIBEJEGI -> PoliceDrinkReportStrategy
2025-08-29 13:34:51.758 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-29 13:34:54.787 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:54.787 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:34:55.388 INFO  [main] easy-es - ===> Not smoothly process index mode activated
2025-08-29 13:34:58.416 ERROR [ForkJoinPool.commonPool-worker-9] easy-es - process index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
2025-08-29 13:34:58.416 WARN  [ForkJoinPool.commonPool-worker-9] easy-es - ===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
2025-08-29 13:35:15.687 INFO  [main] o.s.c.c.u.InetUtils - Cannot determine local hostname
2025-08-29 13:35:15.980 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-29 13:35:15.980 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-29 13:35:17.010 INFO  [main] c.h.n.c.SharedNacosNameSpace - {"secretKey":"","password":"hl123","namespace":"public","accessKey":"","serverAddr":"**************:8848","isUseCloudNamespaceParsing":"false","clusterName":"","username":"nacos"}
2025-08-29 13:35:18.176 INFO  [main] o.s.c.c.u.InetUtils - Cannot determine local hostname
2025-08-29 13:35:18.919 INFO  [main] o.s.c.o.FeignClientFactoryBean - For 'hl-dict' URL not provided. Will try picking an instance via load-balancing.
2025-08-29 13:35:24.010 INFO  [main] o.s.b.a.e.w.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-29 13:35:25.777 INFO  [main] c.h.s.c.SecurityConfig - [/test/dict, /dict/add-batch, /error/logs, /dict/api-query, /dict-test/test] --> 200
2025-08-29 13:35:26.217 INFO  [main] o.s.s.w.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@154a3126, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@65111abe, org.springframework.security.web.context.SecurityContextPersistenceFilter@7b51d80d, org.springframework.security.web.header.HeaderWriterFilter@ca47d17, org.springframework.security.web.authentication.logout.LogoutFilter@2878caf6, org.springframework.web.filter.CorsFilter@2ad43051, com.hl.security.config.sso.SsoAuthTokenFilter@1a2bcce1, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@64b3a31f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@371b39da, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6cb7fb13, org.springframework.security.web.session.SessionManagementFilter@291fd83d, org.springframework.security.web.access.ExceptionTranslationFilter@3f008d2d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@d0c86f7]
2025-08-29 13:35:34.519 INFO  [main] o.a.c.h.Http11NioProtocol - Starting ProtocolHandler ["http-nio-28183"]
2025-08-29 13:35:34.677 INFO  [main] o.s.b.w.e.t.TomcatWebServer - Tomcat started on port(s): 28183 (http) with context path ''
2025-08-29 13:35:35.825 INFO  [main] o.s.c.c.u.InetUtils - Cannot determine local hostname
2025-08-29 13:35:35.833 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-29 13:35:35.833 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-29 13:35:36.099 INFO  [main] c.a.c.n.r.NacosServiceRegistry - nacos registry, default hl-wj-police-archive *************:28183 register finished
2025-08-29 13:35:39.842 INFO  [main] o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [**************:5672]
2025-08-29 13:35:40.252 INFO  [main] o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#27f3be72:0/SimpleConnection@2f9002da [delegate=amqp://admin@**************:5672/, localPort= 58963]
2025-08-29 13:35:40.860 INFO  [main] c.h.AppMain - Started AppMain in 116.589 seconds (JVM running for 126.775)
2025-08-29 13:35:40.863 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-29 13:35:40.863 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-29 13:35:45.373 INFO  [main] c.h.s.c.s.c.SsoCache - init cache_data -> size 21 --> ["user_circle","role","org_job_user","user_resources","project","resources","user_leave","resources_user","user_all","user_role_meet","circle_user","user_role","role_resources","police","role_user","organization","user_org_role","job","circle","user","organization_tree"] -> 
2025-08-29 13:35:45.378 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-29 13:35:45.379 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-29 13:35:45.689 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive.properties, group=default
2025-08-29 13:35:45.689 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive-druid.properties, group=default
2025-08-29 13:35:45.690 INFO  [main] c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=hl-wj-police-archive, group=default
2025-08-29 13:35:45.791 INFO  [main] c.h.d.s.i.DictDataServiceImpl - refresh dict cache 
2025-08-29 13:35:46.344 INFO  [RMI TCP Connection(12)-*************] o.a.c.c.C.[.[.[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-29 13:35:46.344 INFO  [RMI TCP Connection(12)-*************] o.s.w.s.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-29 13:35:46.412 INFO  [RMI TCP Connection(12)-*************] o.s.w.s.DispatcherServlet - Completed initialization in 68 ms
2025-08-29 13:35:47.383 WARN  [RMI TCP Connection(14)-*************] o.s.b.a.e.ElasticsearchRestClientHealthIndicator - Elasticsearch health check failed
java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor142.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-08-29 13:35:47.876 INFO  [main] c.h.l.c.l.LogSql - Execute SQL：SELECT id,dict_type,parent_id,dict_value,dict_name,sort,field_class,remark,create_user,create_time,update_user,update_time,is_disable,tenant_id,is_system,is_default,extend FROM dict_data
2025-08-29 13:35:55.204 ERROR [RMI TCP Connection(14)-*************] c.a.d.f.s.StatFilter - slow sql 1838 millis. SELECT 1[]
2025-08-29 13:35:58.729 INFO  [RMI TCP Connection(14)-*************] c.a.d.p.DruidDataSource - {dataSource-1} inited
2025-08-29 13:35:58.850 WARN  [RMI TCP Connection(14)-*************] o.s.b.a.h.HealthEndpointSupport - Health contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/masterDataSource) took 11242ms to respond
2025-08-29 13:36:03.136 INFO  [RMI TCP Connection(14)-*************] c.a.d.p.DruidDataSource - {dataSource-2} inited
2025-08-29 13:36:07.487 INFO  [RMI TCP Connection(14)-*************] c.a.d.p.DruidDataSource - {dataSource-3} inited
2025-08-29 13:36:12.891 INFO  [RMI TCP Connection(14)-*************] c.a.d.p.DruidDataSource - {dataSource-4} inited
2025-08-29 13:36:16.659 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.TaskEventListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"add\",\"data\":{\"a_cc_idcards\":\"/\",\"a_cc_keys\":\"SBCDEFGH\",\"a_helper_idcards\":\"/\",\"a_helper_keys\":\"@\",\"a_jkry_idcards\":\"/32011111111111111/32040219831018311X/32011419740827003X/320411198502282813/\",\"a_jkry_keys\":\"/SBCDEarchive.adminFGH0I/\",\"a_organization_fj\":\"************\",\"a_organization_pcs\":\"************\",\"a_organization_zrq\":\"\",\"a_work_idcards\":\"/******************/320401199210122812/32042119710723801X/\",\"a_work_keys\":\"488K34VPZF\",\"a_work_now_idcards\":\"/******************/320401199210122812/32042119710723801X/\",\"a_work_now_keys\":\"488K34VPZF\",\"circle_continue\":0,\"circle_num\":1,\"config_id\":\"TC1755148963645JZQ_WJ\",\"config_uuid\":\"CKSWWQQWW7H\",\"content\":{\"config_uuid\":\"CKSWWQQWW7H\",\"FormPersonName_OD_4Go00IyLi83UR\":\"M_hualong\",\"FormPersonOrg_z1kuVpHykRhP0XlT\":\"************\",\"FormPersonJob_lDGxBbl9KvR_RRPk\":\"民警\",\"FormCascaderSelect_rzAIylq9QPZWltOG\":[\"17514501902727715004\"],\"FormDate_9sizHeU70JIOd43k\":\"2025-09-01 00:00:00_2025-09-08 00:00:00\",\"FormInput_fxwe8TJlJRrDj_PJ\":\"11\",\"FormDate_d3Sbseq5u3RONs01\":\"2025-08-14\",\"FormInput_s_Cx1avzRqdZadeO\":\"111\",\"FormInput__GaGMzFBKIoavdx0\":\"11\",\"FormInput_CWtPqt1nhdaDzvkG\":\"11\",\"FormNumberSignature_dWM7EAcWY2Eha0EM\":\"/tyyh/preview/sign/hualong_1755049382627.png\",\"FormSelect_wS87jmOi9WVqkIwB\":\"股级干部\",\"sign_url\":\"M_hualong\",\"xm\":\"M_hualong\",\"dw\":\"************\",\"zw\":\"民警\",\"qwgj\":[\"17514501902727715004\"],\"tlsj\":\"2025-09-01 00:00:00_2025-09-08 00:00:00\",\"jfly\":\"11\",\"lqzjsj\":\"2025-08-14\",\"cgsy\":\"111\",\"yxxjqk\":\"11\",\"syxjqk\":\"11\",\"sqrqm\":\"/tyyh/preview/sign/hualong_1755049382627.png\",\"CKSWWQQWW7H_title\":\"M_hualong 因私出国(境)申请\"},\"create_organization\":\"************\",\"create_time\":\"2025-08-29 13:36:22\",\"error_type\":0,\"extend_str\":\"1756445782959OUQV1BV\",\"has_additional\":0,\"has_pz\":0,\"id_card\":\"hualong\",\"is_circle\":0,\"is_delete\":0,\"is_error\":0,\"last_time\":\"2025-08-29 13:36:22\",\"organization\":\"/************/\",\"person_info\":{\"6Hqnw_cY4Ryz0hJA\":{\"handler\":{\"488K34VPZF\":\"******************/320401199210122812/32042119710723801X\"},\"works\":\"******************/320401199210122812/32042119710723801X\"}},\"process_num\":1,\"status\":\"UNFINISH\",\"task_id\":\"1756445782959EAQNZ\",\"title\":\"M_hualong 因私出国(境)申请\",\"update_time\":\"2025-08-29 13:36:22\",\"work_starttime\":\"2025-08-29 13:36:22\",\"type\":\"add\",\"time\":\"2025-08-29 13:36:23\"}}","content_time":"2025-08-29 13:36:24","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"d4cbdb9c-0ad9-445b-aa46-86efa8a0af96"}
2025-08-29 13:36:17.240 INFO  [pool-20-thread-1] c.h.a.l.TaskEventListener - 使用策略处理任务: configUuid=CKSWWQQWW7H, opt=add, strategy=OverseasTravelStrategy
2025-08-29 13:36:17.242 INFO  [pool-20-thread-1] c.h.a.l.s.i.OverseasTravelStrategy - 处理因私出国任务，操作类型: add
2025-08-29 13:36:25.268 WARN  [RMI TCP Connection(14)-*************] o.s.b.a.h.HealthEndpointSupport - Health contributor org.springframework.boot.actuate.autoconfigure.health.HealthEndpointConfiguration$AdaptedReactiveHealthContributors$1 (redis) took 12318ms to respond
2025-08-29 13:38:34.087 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.TaskEventListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"work\",\"data\":{\"content\":{\"FormDate_XdyoIf3Dx2SWRmJe\":\"2025-08-07\",\"id\":\"\",\"zjffsj\":\"2025-08-07\",\"id_card\":\"32040219831018311X\"},\"id_card\":\"32040219831018311X\",\"node_id\":\"6oVFCldrTkMjA0ed\",\"result_id\":\"\",\"task_id\":\"175523474316761LGC\",\"config_uuid\":\"CKSWWQQWW7H\",\"type\":\"work\",\"time\":\"2025-08-29 13:38:40\",\"form_field_list\":[{\"_style\":{},\"_property\":{\"label\":\"证件发放时间\",\"label_width\":\"\",\"label_id\":\"zjffsj\",\"guide\":\"\",\"date_type\":\"date\",\"required\":true,\"date_format\":\"YYYY-MM-DD\",\"is_range\":false,\"editable\":true,\"show\":[],\"status\":1,\"is_sort\":0,\"is_two_column\":1,\"default_value\":{\"type\":\"datetime\"},\"is_default_search\":0},\"id\":\"FormDate_XdyoIf3Dx2SWRmJe\",\"key\":\"FormDate\"}],\"custom_id\":\"6oVFCldrTkMjA0ed\"}}","content_time":"2025-08-29 13:38:41","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"19644244-7402-428c-b3d0-c90ca9f54dfd"}
2025-08-29 13:38:34.093 INFO  [pool-20-thread-2] c.h.a.l.TaskEventListener - 使用策略处理任务: configUuid=CKSWWQQWW7H, opt=work, strategy=OverseasTravelStrategy
2025-08-29 13:38:34.093 INFO  [pool-20-thread-2] c.h.a.l.s.i.OverseasTravelStrategy - 处理因私出国任务，操作类型: work
2025-08-29 13:40:56.415 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] c.h.a.l.TaskEventListener - 接收到消息: {"content_data":"{\"type\":\"operate\",\"opt\":\"audit\",\"data\":{\"content\":{\"pass\":1,\"text\":\"11\",\"sign_url\":\"/tyyh/preview/sign/32011419740827003X_1755133592424.png\",\"id_card\":\"32011419740827003X\"},\"node_id\":\"jIeYxcy6jYMhPQm5\",\"result_id\":\"\",\"task_id\":\"1756445782959EAQNZ\",\"id_card\":\"32011419740827003X\",\"config_uuid\":\"CKSWWQQWW7H\",\"type\":\"audit\",\"time\":\"2025-08-29 13:41:02\",\"form_field_list\":[{\"_style\":{},\"_property\":{\"editable\":true,\"label\":\"是否通过\",\"label_width\":\"\",\"label_id\":\"\",\"placeholder\":\"\",\"expand\":true,\"guide\":\"\",\"format\":\"\",\"required\":true,\"readonly\":true,\"is_digital_signature\":1,\"default_value\":{\"type\":\"custome\",\"value\":\"\"},\"options\":[{\"label\":\"通过\",\"value\":1},{\"label\":\"驳回\",\"value\":0}],\"status\":1,\"is_use_seal\":0,\"is_use_name\":1,\"no_sign_use_police\":0},\"id\":\"pass\",\"key\":\"FormSelect\",\"notSubmit\":false},{\"_style\":{},\"_property\":{\"editable\":true,\"label\":\"审批说明\",\"label_width\":\"\",\"label_id\":\"\",\"placeholder\":\"\",\"guide\":\"\",\"format\":\"\",\"required\":false,\"readonly\":true,\"default_value\":{\"type\":\"custome\",\"value\":\"\"},\"status\":1},\"id\":\"text\",\"key\":\"FormTextarea\",\"notSubmit\":false}],\"custom_id\":\"ZWSP\"}}","content_time":"2025-08-29 13:41:04","direction":2,"is_apply":1,"plugin":"TaskRabbitPlugin","project_id":"archive","topic":"wj_task_archive_s","uuid":"77309488-f100-4d12-a9db-3db3800b0ec5"}
2025-08-29 13:40:56.416 INFO  [pool-20-thread-3] c.h.a.l.TaskEventListener - 使用策略处理任务: configUuid=CKSWWQQWW7H, opt=audit, strategy=OverseasTravelStrategy
2025-08-29 13:40:56.416 INFO  [pool-20-thread-3] c.h.a.l.s.i.OverseasTravelStrategy - 处理因私出国任务，操作类型: audit
2025-08-29 13:44:25.556 INFO  [pool-20-thread-3] c.h.l.c.l.LogSql - Execute SQL：INSERT INTO police_overseas_travel ( id_card, destination_country, start_date, end_date, travel_reason, data_type, task_id ) VALUES ( 'hualong', '蒙古', '2025-09-01T00:00', '2025-09-08T00:00', '111', 1, '1756445782959EAQNZ' )
2025-08-29 13:45:42.488 WARN  [Thread-32] c.a.n.c.n.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-29 13:45:42.488 WARN  [Thread-32] c.a.n.c.n.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-29 13:45:42.488 WARN  [Thread-26] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-29 13:45:42.495 WARN  [Thread-26] c.a.n.c.h.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-29 13:45:42.538 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-08-29 13:45:42.541 INFO  [SpringApplicationShutdownHook] o.s.b.w.e.t.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-29 13:45:42.625 INFO  [tomcat-shutdown] o.s.b.w.e.t.GracefulShutdown - Graceful shutdown complete
2025-08-29 13:45:43.352 INFO  [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-08-29 13:45:43.407 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-08-29 13:45:43.411 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-08-29 13:45:43.740 INFO  [SpringApplicationShutdownHook] c.h.c.t.GraceThreadPool - ==== 优雅关闭线程池 ====
2025-08-29 13:45:43.753 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-4} closing ...
2025-08-29 13:45:43.784 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-4} closed
2025-08-29 13:45:43.784 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-3} closing ...
2025-08-29 13:45:43.792 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-3} closed
2025-08-29 13:45:43.792 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-2} closing ...
2025-08-29 13:45:43.814 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-2} closed
2025-08-29 13:45:43.815 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-1} closing ...
2025-08-29 13:45:43.822 INFO  [SpringApplicationShutdownHook] c.a.d.p.DruidDataSource - {dataSource-1} closed
