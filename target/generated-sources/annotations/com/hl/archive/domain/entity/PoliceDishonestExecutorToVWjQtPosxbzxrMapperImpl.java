package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjQtPosxbzxr;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-29T17:20:20+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceDishonestExecutorToVWjQtPosxbzxrMapperImpl implements PoliceDishonestExecutorToVWjQtPosxbzxrMapper {

    @Override
    public VWjQtPosxbzxr convert(PoliceDishonestExecutor source) {
        if ( source == null ) {
            return null;
        }

        VWjQtPosxbzxr vWjQtPosxbzxr = new VWjQtPosxbzxr();

        vWjQtPosxbzxr.setZxjg( source.getExecutionUnit() );
        vWjQtPosxbzxr.setJtqk( source.getDishonestReason() );
        vWjQtPosxbzxr.setGmsfhm( source.getIdCard() );
        vWjQtPosxbzxr.setRygxmc( source.getRelationship() );

        return vWjQtPosxbzxr;
    }

    @Override
    public VWjQtPosxbzxr convert(PoliceDishonestExecutor source, VWjQtPosxbzxr target) {
        if ( source == null ) {
            return target;
        }

        target.setZxjg( source.getExecutionUnit() );
        target.setJtqk( source.getDishonestReason() );
        target.setGmsfhm( source.getIdCard() );
        target.setRygxmc( source.getRelationship() );

        return target;
    }
}
