package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyjdkh;
import com.hl.orasync.domain.VWjRyjdkhToPoliceQuarterlyAssessmentMapper;
import io.github.linpeilie.AutoMapperConfig__408;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__408.class,
    uses = {VWjRyjdkhToPoliceQuarterlyAssessmentMapper.class},
    imports = {}
)
public interface PoliceQuarterlyAssessmentToVWjRyjdkhMapper extends BaseMapper<PoliceQuarterlyAssessment, VWjRyjdkh> {
  @Mapping(
      target = "jgmc",
      source = "assessmentResult"
  )
  @Mapping(
      target = "nd",
      source = "assessmentYear"
  )
  @Mapping(
      target = "dwmc",
      source = "assessmentUnit"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "jd",
      source = "assessmentQuarter"
  )
  VWjRyjdkh convert(PoliceQuarterlyAssessment source);

  @Mapping(
      target = "jgmc",
      source = "assessmentResult"
  )
  @Mapping(
      target = "nd",
      source = "assessmentYear"
  )
  @Mapping(
      target = "dwmc",
      source = "assessmentUnit"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "jd",
      source = "assessmentQuarter"
  )
  VWjRyjdkh convert(PoliceQuarterlyAssessment source, @MappingTarget VWjRyjdkh target);
}
