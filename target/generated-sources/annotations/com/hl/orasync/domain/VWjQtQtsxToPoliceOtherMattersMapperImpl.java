package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceOtherMatters;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-29T17:20:20+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjQtQtsxToPoliceOtherMattersMapperImpl implements VWjQtQtsxToPoliceOtherMattersMapper {

    @Override
    public PoliceOtherMatters convert(VWjQtQtsx source) {
        if ( source == null ) {
            return null;
        }

        PoliceOtherMatters policeOtherMatters = new PoliceOtherMatters();

        policeOtherMatters.setReportContent( source.getBz() );
        policeOtherMatters.setIdCard( source.getGmsfhm() );
        policeOtherMatters.setRegistrationDate( ConversionUtils.strToDate( source.getDjrq() ) );

        return policeOtherMatters;
    }

    @Override
    public PoliceOtherMatters convert(VWjQtQtsx source, PoliceOtherMatters target) {
        if ( source == null ) {
            return target;
        }

        target.setReportContent( source.getBz() );
        target.setIdCard( source.getGmsfhm() );
        target.setRegistrationDate( ConversionUtils.strToDate( source.getDjrq() ) );

        return target;
    }
}
