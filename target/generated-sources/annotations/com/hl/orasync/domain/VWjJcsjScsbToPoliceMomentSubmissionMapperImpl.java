package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceMomentSubmission;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-29T17:20:20+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjJcsjScsbToPoliceMomentSubmissionMapperImpl implements VWjJcsjScsbToPoliceMomentSubmissionMapper {

    @Override
    public PoliceMomentSubmission convert(VWjJcsjScsb source) {
        if ( source == null ) {
            return null;
        }

        PoliceMomentSubmission policeMomentSubmission = new PoliceMomentSubmission();

        policeMomentSubmission.setSubmissionType( source.getBslxmc() );
        policeMomentSubmission.setSubmitter( source.getBsrxm() );
        policeMomentSubmission.setMaterialType( source.getCllxmc() );
        policeMomentSubmission.setOfficerName( source.getCjmj() );
        policeMomentSubmission.setSubmitUnit( source.getBsdwmc() );
        policeMomentSubmission.setAuditResult( source.getShjgmc() );
        policeMomentSubmission.setMaterialIntro( source.getScjj() );
        policeMomentSubmission.setSubmissionTime( ConversionUtils.strToDate( source.getDjsj() ) );
        policeMomentSubmission.setZjbh( source.getXxzjbh() );

        return policeMomentSubmission;
    }

    @Override
    public PoliceMomentSubmission convert(VWjJcsjScsb source, PoliceMomentSubmission target) {
        if ( source == null ) {
            return target;
        }

        target.setSubmissionType( source.getBslxmc() );
        target.setSubmitter( source.getBsrxm() );
        target.setMaterialType( source.getCllxmc() );
        target.setOfficerName( source.getCjmj() );
        target.setSubmitUnit( source.getBsdwmc() );
        target.setAuditResult( source.getShjgmc() );
        target.setMaterialIntro( source.getScjj() );
        target.setSubmissionTime( ConversionUtils.strToDate( source.getDjsj() ) );
        target.setZjbh( source.getXxzjbh() );

        return target;
    }
}
