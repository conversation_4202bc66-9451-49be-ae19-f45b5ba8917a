package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PolicePoliticalStatus;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-29T17:20:20+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjRyzzmmToPolicePoliticalStatusMapperImpl implements VWjRyzzmmToPolicePoliticalStatusMapper {

    @Override
    public PolicePoliticalStatus convert(VWjRyzzmm source) {
        if ( source == null ) {
            return null;
        }

        PolicePoliticalStatus policePoliticalStatus = new PolicePoliticalStatus();

        policePoliticalStatus.setJoinPartyDate( ConversionUtils.strToLocalDate( source.getCjdpsj() ) );
        policePoliticalStatus.setPoliticalIdentity( source.getZzsf() );
        policePoliticalStatus.setIdCard( source.getGmsfhm() );

        return policePoliticalStatus;
    }

    @Override
    public PolicePoliticalStatus convert(VWjRyzzmm source, PolicePoliticalStatus target) {
        if ( source == null ) {
            return target;
        }

        target.setJoinPartyDate( ConversionUtils.strToLocalDate( source.getCjdpsj() ) );
        target.setPoliticalIdentity( source.getZzsf() );
        target.setIdCard( source.getGmsfhm() );

        return target;
    }
}
