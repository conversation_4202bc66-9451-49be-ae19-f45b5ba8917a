package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PolicePositionRank;
import com.hl.archive.domain.entity.PolicePositionRankToVWjRyzwzjMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__408;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__408.class,
    uses = {ConversionUtils.class,PolicePositionRankToVWjRyzwzjMapper.class},
    imports = {}
)
public interface VWjRyzwzjToPolicePositionRankMapper extends BaseMapper<VWjRyzwzj, PolicePositionRank> {
  @Mapping(
      target = "policePositionLevel",
      source = "gazwjb"
  )
  @Mapping(
      target = "positionName",
      source = "zwmc"
  )
  @Mapping(
      target = "currentPositionDate",
      source = "zwsxsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "appointmentDocument",
      source = "rzwh"
  )
  @Mapping(
      target = "currentRankDate",
      source = "xzjsj",
      qualifiedByName = {"strToDate"}
  )
  PolicePositionRank convert(VWjRyzwzj source);

  @Mapping(
      target = "policePositionLevel",
      source = "gazwjb"
  )
  @Mapping(
      target = "positionName",
      source = "zwmc"
  )
  @Mapping(
      target = "currentPositionDate",
      source = "zwsxsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "appointmentDocument",
      source = "rzwh"
  )
  @Mapping(
      target = "currentRankDate",
      source = "xzjsj",
      qualifiedByName = {"strToDate"}
  )
  PolicePositionRank convert(VWjRyzwzj source, @MappingTarget PolicePositionRank target);
}
