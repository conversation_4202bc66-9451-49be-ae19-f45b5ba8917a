package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceFamilySecuritiesInsurance;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-29T17:20:20+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjQtGpjjtzToPoliceFamilySecuritiesInsuranceMapperImpl implements VWjQtGpjjtzToPoliceFamilySecuritiesInsuranceMapper {

    @Override
    public PoliceFamilySecuritiesInsurance convert(VWjQtGpjjtz source) {
        if ( source == null ) {
            return null;
        }

        PoliceFamilySecuritiesInsurance policeFamilySecuritiesInsurance = new PoliceFamilySecuritiesInsurance();

        policeFamilySecuritiesInsurance.setNetValuePremium( source.getRjz() );
        policeFamilySecuritiesInsurance.setHolderName( source.getXmCyr() );
        policeFamilySecuritiesInsurance.setSecurityNameCode( source.getMcdm() );
        policeFamilySecuritiesInsurance.setIdCard( source.getGmsfhm() );
        policeFamilySecuritiesInsurance.setHoldingQuantity( source.getSl() );

        return policeFamilySecuritiesInsurance;
    }

    @Override
    public PoliceFamilySecuritiesInsurance convert(VWjQtGpjjtz source, PoliceFamilySecuritiesInsurance target) {
        if ( source == null ) {
            return target;
        }

        target.setNetValuePremium( source.getRjz() );
        target.setHolderName( source.getXmCyr() );
        target.setSecurityNameCode( source.getMcdm() );
        target.setIdCard( source.getGmsfhm() );
        target.setHoldingQuantity( source.getSl() );

        return target;
    }
}
